using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;
using VRC.SDK3.StringLoading;
using System;

namespace TheZiver
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class FishPermissionSystem : UdonSharpBehaviour
    {
        [Header("＞＜＞ PERMISSION TARGETS ＜＞＜")]
        [Tooltip("Prefabs to instantiate for a permitted player. These only exist locally.")]
        public GameObject[] targetPrefabs;
        [Tooltip("Colliders to enable for a permitted player.")]
        public Collider[] targetColliders;
        [Tooltip("GameObjects to enable for a permitted player.")]
        public GameObject[] targetGameObjects;

        [Head<PERSON>("＞＜＞ CONFIGURATION ＜＞＜")]
        [Tooltip("The URL to a plain text file containing comma-separated display names.")]
        public VRCUrl url;

        [Tooltip("The URL to a plain text file containing comma-separated blacklisted display names. If a player is on this list, the script will not activate for them regardless of the permission list.")]
        public VRCUrl blacklistUrl;

        [Tooltip("How long (in seconds) to wait after the world loads before the first download.")]
        [SerializeField]
        private float initialDelay = 5.0f;

        [Tooltip("How often (in seconds) to re-download the list to check for updates.")]
        [SerializeField]
        private float checkInterval = 300.0f;

        // --- MODIFIED LINE ---
        [Tooltip("How long (in seconds) to wait before retrying after a failed download.")]
        [SerializeField]
        private float retryDelay = 120.0f; // Default value is now 120 seconds

        private GameObject[] instantiatedPrefabs;
        private string[] permittedPlayerNames;
        private string[] blacklistedPlayerNames;
        private bool isLoading = false;
        private bool isLoadingBlacklist = false;
        private bool expectingBlacklistResponse = false;
        
        // --- SECURITY WARNING ---
        // This system uses VRChat Display Names for permission and blacklisting. Display names are NOT unique and can be
        // copied by other users. Do not use this to protect critical assets or moderator tools.
        // This is suitable for low-stakes, non-essential features only.
        // The blacklist feature provides an additional layer of control but has the same security limitations.

        void Start()
        {
            instantiatedPrefabs = new GameObject[targetPrefabs.Length];
            SendCustomEventDelayedSeconds(nameof(LoadUrlContent), initialDelay);

            // Load blacklist if URL is provided
            if (blacklistUrl != null && !string.IsNullOrEmpty(blacklistUrl.Get()))
            {
                SendCustomEventDelayedSeconds(nameof(LoadBlacklistContent), initialDelay + 1.0f);
            }
        }

        public void LoadUrlContent()
        {
            if (isLoading) return;

            isLoading = true;
            Debug.Log("[FishPermissionSystem] Loading permission list from URL...");
            VRCStringDownloader.LoadUrl(url, (IUdonEventReceiver)this);
        }

        public void LoadBlacklistContent()
        {
            if (isLoadingBlacklist) return;
            if (blacklistUrl == null || string.IsNullOrEmpty(blacklistUrl.Get())) return;

            isLoadingBlacklist = true;
            expectingBlacklistResponse = true;
            Debug.Log("[FishPermissionSystem] Loading blacklist from URL...");
            VRCStringDownloader.LoadUrl(blacklistUrl, (IUdonEventReceiver)this);
        }

        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            string listContent = result.Result;

            if (expectingBlacklistResponse)
            {
                isLoadingBlacklist = false;
                expectingBlacklistResponse = false;
                Debug.Log("[FishPermissionSystem] Successfully loaded blacklist.");

                blacklistedPlayerNames = listContent.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                // Trim whitespace from blacklist names
                for (int i = 0; i < blacklistedPlayerNames.Length; i++)
                {
                    blacklistedPlayerNames[i] = blacklistedPlayerNames[i].Trim();
                }

                _CheckAndApplyPermissions();

                // Schedule next blacklist refresh
                if (blacklistUrl != null && !string.IsNullOrEmpty(blacklistUrl.Get()))
                {
                    Debug.Log($"[FishPermissionSystem] Scheduling next blacklist refresh in {checkInterval} seconds.");
                    SendCustomEventDelayedSeconds(nameof(LoadBlacklistContent), checkInterval);
                }
            }
            else
            {
                isLoading = false;
                Debug.Log("[FishPermissionSystem] Successfully loaded permission list.");

                permittedPlayerNames = listContent.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

                // Trim whitespace from permission names
                for (int i = 0; i < permittedPlayerNames.Length; i++)
                {
                    permittedPlayerNames[i] = permittedPlayerNames[i].Trim();
                }

                _CheckAndApplyPermissions();

                Debug.Log($"[FishPermissionSystem] Scheduling next refresh in {checkInterval} seconds.");
                SendCustomEventDelayedSeconds(nameof(LoadUrlContent), checkInterval);
            }
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            if (expectingBlacklistResponse)
            {
                isLoadingBlacklist = false;
                expectingBlacklistResponse = false;
                Debug.LogError($"[FishPermissionSystem] Failed to load blacklist URL: {result.Error}");

                Debug.Log($"[FishPermissionSystem] Scheduling blacklist retry in {retryDelay} seconds.");
                SendCustomEventDelayedSeconds(nameof(LoadBlacklistContent), retryDelay);
            }
            else
            {
                isLoading = false;
                Debug.LogError($"[FishPermissionSystem] Failed to load permission URL: {result.Error}");

                Debug.Log($"[FishPermissionSystem] Scheduling permission retry in {retryDelay} seconds.");
                SendCustomEventDelayedSeconds(nameof(LoadUrlContent), retryDelay);
            }
        }

        private void _CheckAndApplyPermissions()
        {
            VRCPlayerApi localPlayer = Networking.LocalPlayer;
            if (localPlayer == null) return;

            string playerName = localPlayer.displayName;

            // Check blacklist first - if player is blacklisted, deny access regardless of permission list
            bool isBlacklisted = false;
            if (blacklistedPlayerNames != null)
            {
                isBlacklisted = Array.IndexOf(blacklistedPlayerNames, playerName) > -1;
            }

            if (isBlacklisted)
            {
                Debug.Log($"[FishPermissionSystem] Player {playerName} is blacklisted. Denying access.");

                // Disable everything for blacklisted players
                foreach (GameObject obj in targetGameObjects)
                {
                    if (obj != null) obj.SetActive(false);
                }

                foreach (Collider col in targetColliders)
                {
                    if (col != null) col.enabled = false;
                }

                for (int i = 0; i < targetPrefabs.Length; i++)
                {
                    if (instantiatedPrefabs[i] != null)
                    {
                        Destroy(instantiatedPrefabs[i]);
                        instantiatedPrefabs[i] = null;
                    }
                }
                return; // Exit early for blacklisted players
            }

            // If not blacklisted, check permissions normally
            if (permittedPlayerNames == null) return;

            bool hasPermission = Array.IndexOf(permittedPlayerNames, playerName) > -1;

            Debug.Log($"[FishPermissionSystem] Checking permissions for {playerName}. Result: {hasPermission}");

            foreach (GameObject obj in targetGameObjects)
            {
                if (obj != null) obj.SetActive(hasPermission);
            }

            foreach (Collider col in targetColliders)
            {
                if (col != null) col.enabled = hasPermission;
            }

            for (int i = 0; i < targetPrefabs.Length; i++)
            {
                if (hasPermission && instantiatedPrefabs[i] == null)
                {
                    if(targetPrefabs[i] != null)
                    {
                        instantiatedPrefabs[i] = VRCInstantiate(targetPrefabs[i]);
                    }
                }
                else if (!hasPermission && instantiatedPrefabs[i] != null)
                {
                    Destroy(instantiatedPrefabs[i]);
                    instantiatedPrefabs[i] = null;
                }
            }
        }
    }
}