﻿using UdonSharp;
using UnityEngine;
using VRC.SDKBase;
using VRC.Udon;
using VRC.Udon.Common.Interfaces;
using VRC.SDK3.StringLoading;
using System;

namespace TheZiver
{
    [UdonBehaviourSyncMode(BehaviourSyncMode.None)]
    public class FishPermissionSystem : UdonSharpBehaviour
    {
        [Header("＞＜＞ PERMISSION TARGETS ＜＞＜")]
        [Tooltip("Prefabs to instantiate for a permitted player. These only exist locally.")]
        public GameObject[] targetPrefabs;
        [Tooltip("Colliders to enable for a permitted player.")]
        public Collider[] targetColliders;
        [Tooltip("GameObjects to enable for a permitted player.")]
        public GameObject[] targetGameObjects;

        [Head<PERSON>("＞＜＞ CONFIGURATION ＜＞＜")]
        [Tooltip("The URL to a plain text file containing comma-separated display names.")]
        public VRCUrl url;
        
        [Tooltip("How long (in seconds) to wait after the world loads before the first download.")]
        [SerializeField]
        private float initialDelay = 5.0f;

        [Toolt<PERSON>("How often (in seconds) to re-download the list to check for updates.")]
        [SerializeField]
        private float checkInterval = 300.0f;
        
        // --- MODIFIED LINE ---
        [Tooltip("How long (in seconds) to wait before retrying after a failed download.")]
        [SerializeField]
        private float retryDelay = 120.0f; // Default value is now 120 seconds

        private GameObject[] instantiatedPrefabs;
        private string[] permittedPlayerNames;
        private bool isLoading = false;
        
        // --- SECURITY WARNING ---
        // This system uses VRChat Display Names for permission. Display names are NOT unique and can be
        // copied by other users. Do not use this to protect critical assets or moderator tools.
        // This is suitable for low-stakes, non-essential features only.

        void Start()
        {
            instantiatedPrefabs = new GameObject[targetPrefabs.Length];
            SendCustomEventDelayedSeconds(nameof(LoadUrlContent), initialDelay);
        }

        public void LoadUrlContent()
        {
            if (isLoading) return;

            isLoading = true;
            Debug.Log("[FishPermissionSystem] Loading permission list from URL...");
            VRCStringDownloader.LoadUrl(url, (IUdonEventReceiver)this);
        }

        public override void OnStringLoadSuccess(IVRCStringDownload result)
        {
            isLoading = false;
            Debug.Log("[FishPermissionSystem] Successfully loaded permission list.");
            
            string listContent = result.Result;
            permittedPlayerNames = listContent.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);

            _CheckAndApplyPermissions();
            
            Debug.Log($"[FishPermissionSystem] Scheduling next refresh in {checkInterval} seconds.");
            SendCustomEventDelayedSeconds(nameof(LoadUrlContent), checkInterval);
        }

        public override void OnStringLoadError(IVRCStringDownload result)
        {
            isLoading = false;
            Debug.LogError($"[FishPermissionSystem] Failed to load URL: {result.Error}");

            Debug.Log($"[FishPermissionSystem] Scheduling a retry in {retryDelay} seconds.");
            SendCustomEventDelayedSeconds(nameof(LoadUrlContent), retryDelay);
        }

        private void _CheckAndApplyPermissions()
        {
            VRCPlayerApi localPlayer = Networking.LocalPlayer;
            if (localPlayer == null) return;

            if (permittedPlayerNames == null) return; 

            bool hasPermission = Array.IndexOf(permittedPlayerNames, localPlayer.displayName) > -1;

            Debug.Log($"[FishPermissionSystem] Checking permissions for {localPlayer.displayName}. Result: {hasPermission}");

            foreach (GameObject obj in targetGameObjects)
            {
                if (obj != null) obj.SetActive(hasPermission);
            }

            foreach (Collider col in targetColliders)
            {
                if (col != null) col.enabled = hasPermission;
            }

            for (int i = 0; i < targetPrefabs.Length; i++)
            {
                if (hasPermission && instantiatedPrefabs[i] == null)
                {
                    if(targetPrefabs[i] != null)
                    {
                        instantiatedPrefabs[i] = VRCInstantiate(targetPrefabs[i]);
                    }
                }
                else if (!hasPermission && instantiatedPrefabs[i] != null)
                {
                    Destroy(instantiatedPrefabs[i]);
                    instantiatedPrefabs[i] = null;
                }
            }
        }
    }
}